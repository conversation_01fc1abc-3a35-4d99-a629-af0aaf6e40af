class NewNotesManager {
    constructor() {
        this.notes = new Map();
        this.currentNoteId = null;
        this.joditEditor = null;
        this.autoSaveInterval = null;
        this.saveTimeout = null;
        this.renderTimeout = null;
        this.init();
    }

    init() {
        this.loadNotesFromStorage();
        this.setupEventListeners();
        this.initializeJoditEditor();
        this.renderNotes();
        this.startAutoSave();
        this.initializeSelectionPopup();
    }

    setupEventListeners() {
        // Add new note button
        document.getElementById('addNewNote').addEventListener('click', () => {
            this.createNewNote();
        });

        // Close editor
        document.getElementById('closeEditor').addEventListener('click', () => {
            this.closeEditor();
        });

        // Save note
        document.getElementById('saveNote').addEventListener('click', () => {
            this.saveCurrentNote();
        });

        // Delete note
        document.getElementById('deleteNote').addEventListener('click', () => {
            this.deleteCurrentNote();
        });

        // Insert image
        document.getElementById('insertImage').addEventListener('click', () => {
            this.insertImage();
        });

        // Insert date time
        document.getElementById('insertDateTime').addEventListener('click', () => {
            this.insertDateTime();
        });

        // Toggle fullscreen
        document.getElementById('toggleFullscreen').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Print note
        document.getElementById('printNote').addEventListener('click', () => {
            this.printCurrentNote();
        });

        // Note title input
        document.getElementById('noteTitle').addEventListener('input', () => {
            this.updateNoteTitle();
            // Instant save on title change
            this.saveCurrentNote();
        });

        // Priority selector
        document.getElementById('notePriority').addEventListener('change', () => {
            this.updateNotePriority();
            // Instant save on priority change
            this.saveCurrentNote();
        });

        // Sort selector
        document.getElementById('noteSortSelect').addEventListener('change', () => {
            this.renderNotes();
        });

        // Close modal on outside click
        document.getElementById('noteEditorModal').addEventListener('click', (e) => {
            if (e.target.id === 'noteEditorModal') {
                this.closeEditor();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveCurrentNote();
            }
            if (e.key === 'Escape') {
                this.closeEditor();
            }
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
        });

        // Window resize listener (removed to prevent issues)
        // CSS handles responsive design
    }

    initializeJoditEditor() {
        const config = {
            height: 450,
            language: 'en',
            toolbarSticky: false,
            showCharsCounter: false,
            showWordsCounter: false,
            showXPathInStatusbar: false,
            askBeforePasteHTML: false,
            askBeforePasteFromWord: false,
            defaultActionOnPaste: 'insert_clear_html',
            autofocus: true,
            spellcheck: true,
            // Enhanced keyboard support
            enter: 'P', // Use <p> tags for Enter
            tabIndex: 1,
            allowTabNavigation: true,
            // Better keyboard handling
            processPasteHTML: false,
            buttons: [
                'bold', 'italic', 'underline', '|',
                'ul', 'ol', '|',
                'font', 'fontsize', '|',
                'brush', 'paragraph', '|',
                'image', 'link', '|',
                'align', 'undo', 'redo', '|',
                'hr', 'table'
            ],
            uploader: {
                insertImageAsBase64URI: true
            },
            filebrowser: {
                ajax: {
                    url: 'data:application/json,[]'
                }
            },
            events: {
                afterInsertImage: (image) => {
                    this.setupImageControls(image);
                }
            }
        };

        this.joditEditor = Jodit.make('#joditEditor', config);

        // Listen for content changes
        this.joditEditor.events.on('change', () => {
            this.updateWordCount();
            this.markAsModified();
            // Debounced auto-save to prevent screen flicker
            this.debouncedSave();
        });

        // Setup existing images on load
        this.joditEditor.events.on('afterSetMode', () => {
            this.setupAllImageControls();
        });

        // Listen for text selection
        this.joditEditor.events.on('mouseup', () => {
            setTimeout(() => this.handleTextSelection(), 10);
        });

        this.joditEditor.events.on('keyup', () => {
            setTimeout(() => this.handleTextSelection(), 10);
        });

        // Hide popup when editor loses focus
        this.joditEditor.events.on('blur', () => {
            setTimeout(() => this.hideSelectionPopup(), 100);
        });

        // Enhanced keyboard support
        this.joditEditor.events.on('keydown', (e) => {
            // Handle Tab key for indentation
            if (e.key === 'Tab') {
                e.preventDefault();
                e.stopPropagation();

                // Insert tab or handle indentation
                const selection = this.joditEditor.selection;
                if (selection) {
                    if (e.shiftKey) {
                        // Shift+Tab: Outdent
                        this.joditEditor.execCommand('outdent');
                    } else {
                        // Tab: Indent or insert tab
                        const range = selection.range;
                        if (range && range.collapsed) {
                            // Insert tab character
                            this.joditEditor.selection.insertHTML('&nbsp;&nbsp;&nbsp;&nbsp;');
                        } else {
                            // Indent selected text
                            this.joditEditor.execCommand('indent');
                        }
                    }
                }
                return false;
            }

            if (e.key === 'Enter') {
                // Let Jodit handle Enter naturally
                return true;
            }

            // Handle other keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 's':
                        e.preventDefault();
                        this.saveCurrentNote();
                        this.showCommandFeedback('নোট সংরক্ষণ করা হয়েছে');
                        break;
                }
            }
        });

        // Apply dark mode if active
        this.applyThemeToEditor();
    }



    applyThemeToEditor() {
        if (!this.joditEditor) return;

        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        const editorContainer = this.joditEditor.container;

        if (isDarkMode) {
            editorContainer.classList.add('jodit-dark-theme');
        } else {
            editorContainer.classList.remove('jodit-dark-theme');
        }
    }

    initializeSelectionPopup() {
        this.selectionPopup = document.getElementById('selectionPopupToolbar');
        this.isPopupVisible = false;
        this.selectedText = '';
        this.selectionRange = null;
        this.lastMouseX = 0;
        this.lastMouseY = 0;

        // Setup event listeners for popup buttons
        this.setupPopupEventListeners();

        // Track mouse position
        document.addEventListener('mousemove', (e) => {
            this.lastMouseX = e.clientX;
            this.lastMouseY = e.clientY;
        });
    }

    setupPopupEventListeners() {
        // Helper function to safely add event listeners
        const addSafeEventListener = (id, callback) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('click', callback);
            }
        };

        // Copy button
        addSafeEventListener('popupCopy', () => {
            this.executeCommand('copy');
        });

        // Cut button
        addSafeEventListener('popupCut', () => {
            this.executeCommand('cut');
        });

        // Paste button
        addSafeEventListener('popupPaste', () => {
            this.executeCommand('paste');
        });

        // Bold button
        addSafeEventListener('popupBold', () => {
            this.executeCommand('bold');
        });

        // Italic button
        addSafeEventListener('popupItalic', () => {
            this.executeCommand('italic');
        });

        // Underline button
        addSafeEventListener('popupUnderline', () => {
            this.executeCommand('underline');
        });

        // Undo button
        addSafeEventListener('popupUndo', () => {
            this.executeCommand('undo');
        });

        // Redo button
        addSafeEventListener('popupRedo', () => {
            this.executeCommand('redo');
        });

        // Delete button
        addSafeEventListener('popupDelete', () => {
            this.executeCommand('delete');
        });

        // Select all button
        addSafeEventListener('popupSelectAll', () => {
            this.executeCommand('selectAll');
        });

        // Strikethrough button
        addSafeEventListener('popupStrikethrough', () => {
            this.executeCommand('strikethrough');
        });

        // Highlight button
        addSafeEventListener('popupHighlight', () => {
            this.executeCommand('highlight');
        });

        // Text color button
        addSafeEventListener('popupTextColor', () => {
            this.executeCommand('textColor');
        });

        // Font size button
        addSafeEventListener('popupFontSize', () => {
            this.executeCommand('fontSize');
        });

        // Link button
        addSafeEventListener('popupLink', () => {
            this.executeCommand('link');
        });

        // Quote button (if exists)
        addSafeEventListener('popupQuote', () => {
            this.executeCommand('quote');
        });

        // Code button (if exists)
        addSafeEventListener('popupCode', () => {
            this.executeCommand('code');
        });



        // Hide popup when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.selectionPopup.contains(e.target) && this.isPopupVisible) {
                this.hideSelectionPopup();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.isPopupVisible && this.joditEditor && this.joditEditor.editor.contains(document.activeElement)) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key.toLowerCase()) {
                        case 'c':
                            e.preventDefault();
                            this.executeCommand('copy');
                            break;
                        case 'x':
                            e.preventDefault();
                            this.executeCommand('cut');
                            break;
                        case 'v':
                            e.preventDefault();
                            this.executeCommand('paste');
                            break;
                        case 'b':
                            e.preventDefault();
                            this.executeCommand('bold');
                            break;
                        case 'i':
                            e.preventDefault();
                            this.executeCommand('italic');
                            break;
                        case 'u':
                            e.preventDefault();
                            this.executeCommand('underline');
                            break;
                        case 'z':
                            e.preventDefault();
                            this.executeCommand('undo');
                            break;
                        case 'y':
                            e.preventDefault();
                            this.executeCommand('redo');
                            break;
                    }
                } else if (e.key === 'Delete' || e.key === 'Backspace') {
                    if (window.getSelection().toString()) {
                        e.preventDefault();
                        this.executeCommand('delete');
                    }
                }
            }
        });
    }

    executeCommand(command) {
        if (!this.joditEditor) return;

        try {
            switch (command) {
                case 'copy':
                    document.execCommand('copy');
                    this.showCommandFeedback('কপি করা হয়েছে');
                    break;
                case 'cut':
                    document.execCommand('cut');
                    this.showCommandFeedback('কাট করা হয়েছে');
                    this.hideSelectionPopup();
                    break;
                case 'paste':
                    navigator.clipboard.readText().then(text => {
                        this.joditEditor.selection.insertHTML(text);
                        this.showCommandFeedback('পেস্ট করা হয়েছে');
                    }).catch(() => {
                        document.execCommand('paste');
                        this.showCommandFeedback('পেস্ট করা হয়েছে');
                    });
                    break;
                case 'bold':
                    this.joditEditor.execCommand('bold');
                    this.updateFormatButtons();
                    break;
                case 'italic':
                    this.joditEditor.execCommand('italic');
                    this.updateFormatButtons();
                    break;
                case 'underline':
                    this.joditEditor.execCommand('underline');
                    this.updateFormatButtons();
                    break;
                case 'undo':
                    this.joditEditor.execCommand('undo');
                    this.showCommandFeedback('আনডু করা হয়েছে');
                    this.hideSelectionPopup();
                    break;
                case 'redo':
                    this.joditEditor.execCommand('redo');
                    this.showCommandFeedback('রিডু করা হয়েছে');
                    this.hideSelectionPopup();
                    break;
                case 'delete':
                    this.joditEditor.selection.remove();
                    this.showCommandFeedback('ডিলেট করা হয়েছে');
                    this.hideSelectionPopup();
                    break;
                case 'strikethrough':
                    this.joditEditor.execCommand('strikethrough');
                    this.updateFormatButtons();
                    break;
                case 'highlight':
                    this.joditEditor.execCommand('backColor', false, '#ffff00');
                    this.showCommandFeedback('হাইলাইট করা হয়েছে');
                    break;
                case 'textColor':
                    this.showColorPicker('foreColor');
                    break;
                case 'fontSize':
                    this.showFontSizePicker();
                    break;
                case 'link':
                    this.insertLink();
                    break;
                case 'quote':
                    this.joditEditor.execCommand('formatBlock', false, 'blockquote');
                    this.showCommandFeedback('কোট যোগ করা হয়েছে');
                    break;
                case 'code':
                    this.joditEditor.execCommand('formatBlock', false, 'pre');
                    this.showCommandFeedback('কোড ফরম্যাট করা হয়েছে');
                    break;
                case 'selectAll':
                    this.joditEditor.execCommand('selectAll');
                    this.showCommandFeedback('সব সিলেক্ট করা হয়েছে');
                    break;
            }
        } catch (error) {
            console.error('Command execution error:', error);
        }
    }

    showCommandFeedback(message) {
        // Create a temporary feedback element
        const feedback = document.createElement('div');
        feedback.textContent = message;
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2ecc71;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10001;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(feedback);

        setTimeout(() => {
            feedback.remove();
        }, 2000);
    }

    updateFormatButtons() {
        if (!this.joditEditor) return;

        // Update button states based on current formatting
        const isBold = this.joditEditor.execCommand('queryCommandState', 'bold');
        const isItalic = this.joditEditor.execCommand('queryCommandState', 'italic');
        const isUnderline = this.joditEditor.execCommand('queryCommandState', 'underline');

        document.getElementById('popupBold').classList.toggle('active', isBold);
        document.getElementById('popupItalic').classList.toggle('active', isItalic);
        document.getElementById('popupUnderline').classList.toggle('active', isUnderline);

        // Update strikethrough state
        const isStrikethrough = this.joditEditor.execCommand('queryCommandState', 'strikethrough');
        document.getElementById('popupStrikethrough').classList.toggle('active', isStrikethrough);
    }

    showColorPicker(command) {
        const colors = ['#000000', '#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff', '#ffffff'];
        const colorPicker = document.createElement('div');
        colorPicker.style.cssText = `
            position: fixed;
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 10001;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
        `;

        colors.forEach(color => {
            const colorBtn = document.createElement('button');
            colorBtn.style.cssText = `
                width: 30px;
                height: 30px;
                background: ${color};
                border: 2px solid #ddd;
                border-radius: 4px;
                cursor: pointer;
            `;
            colorBtn.addEventListener('click', () => {
                this.joditEditor.execCommand(command, false, color);
                this.showCommandFeedback('রঙ পরিবর্তন করা হয়েছে');
                colorPicker.remove();
            });
            colorPicker.appendChild(colorBtn);
        });

        // Position near the popup
        const rect = this.selectionPopup.getBoundingClientRect();
        colorPicker.style.left = `${rect.right + 10}px`;
        colorPicker.style.top = `${rect.top}px`;

        document.body.appendChild(colorPicker);

        // Remove on click outside
        setTimeout(() => {
            document.addEventListener('click', function removeColorPicker(e) {
                if (!colorPicker.contains(e.target)) {
                    colorPicker.remove();
                    document.removeEventListener('click', removeColorPicker);
                }
            });
        }, 100);
    }

    showFontSizePicker() {
        const sizes = ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px'];
        const sizePicker = document.createElement('div');
        sizePicker.style.cssText = `
            position: fixed;
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 10001;
            min-width: 120px;
        `;

        sizes.forEach(size => {
            const sizeBtn = document.createElement('button');
            sizeBtn.textContent = size;
            sizeBtn.style.cssText = `
                display: block;
                width: 100%;
                padding: 8px;
                border: none;
                background: transparent;
                cursor: pointer;
                text-align: left;
                border-radius: 4px;
            `;
            sizeBtn.addEventListener('mouseover', () => {
                sizeBtn.style.background = '#f0f0f0';
            });
            sizeBtn.addEventListener('mouseout', () => {
                sizeBtn.style.background = 'transparent';
            });
            sizeBtn.addEventListener('click', () => {
                this.joditEditor.execCommand('fontSize', false, size);
                this.showCommandFeedback('ফন্ট সাইজ পরিবর্তন করা হয়েছে');
                sizePicker.remove();
            });
            sizePicker.appendChild(sizeBtn);
        });

        // Position near the popup
        const rect = this.selectionPopup.getBoundingClientRect();
        sizePicker.style.left = `${rect.right + 10}px`;
        sizePicker.style.top = `${rect.top}px`;

        document.body.appendChild(sizePicker);

        // Remove on click outside
        setTimeout(() => {
            document.addEventListener('click', function removeSizePicker(e) {
                if (!sizePicker.contains(e.target)) {
                    sizePicker.remove();
                    document.removeEventListener('click', removeSizePicker);
                }
            });
        }, 100);
    }

    insertLink() {
        const url = prompt('লিংক URL লিখুন:');
        if (url) {
            this.joditEditor.execCommand('createLink', false, url);
            this.showCommandFeedback('লিংক যোগ করা হয়েছে');
        }
    }

    showSelectionPopup(x, y) {
        if (!this.selectionPopup) return;

        // Adjust position to keep popup on screen
        const popupWidth = 250;
        const popupHeight = 120; // Compact height
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Position to the right of cursor close by
        let left = x + 15; // Close to cursor (15px)
        let top = y - 5; // Very close to cursor level

        // Keep popup within viewport
        if (left + popupWidth > viewportWidth - 20) {
            left = x - popupWidth - 15; // Position to the left instead with same close distance
        }
        if (top + popupHeight > viewportHeight - 20) {
            top = y - popupHeight - 20; // Position above instead
        }
        if (left < 20) {
            left = 20;
        }
        if (top < 20) {
            top = 20;
        }

        this.selectionPopup.style.left = `${left}px`;
        this.selectionPopup.style.top = `${top}px`;
        this.selectionPopup.classList.add('show');
        this.isPopupVisible = true;

        // Update format button states
        this.updateFormatButtons();
    }

    hideSelectionPopup() {
        if (!this.selectionPopup) return;

        this.selectionPopup.classList.remove('show');
        this.isPopupVisible = false;
    }

    handleTextSelection() {
        const selection = window.getSelection();

        if (selection.rangeCount > 0 && !selection.isCollapsed) {
            const range = selection.getRangeAt(0);

            // Check if selection is within the editor
            const editorElement = this.joditEditor.editor;
            if (editorElement && editorElement.contains(range.commonAncestorContainer)) {
                this.selectedText = selection.toString();
                this.selectionRange = range;

                // Use mouse position for popup location
                const x = this.lastMouseX;
                const y = this.lastMouseY;

                this.showSelectionPopup(x, y);
            }
        } else {
            this.hideSelectionPopup();
        }
    }

    createNewNote() {
        const noteId = 'note_' + Date.now();
        const note = {
            id: noteId,
            title: 'নতুন নোট',
            content: '',
            priority: 'medium',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.notes.set(noteId, note);
        this.saveNotesToStorage();
        this.forceRender(); // Force render for new note
        this.openEditor(noteId);
    }

    openEditor(noteId) {
        const note = this.notes.get(noteId);
        if (!note) return;

        this.currentNoteId = noteId;

        // Set note data
        document.getElementById('noteTitle').value = note.title;
        document.getElementById('notePriority').value = note.priority || 'medium';
        this.joditEditor.value = note.content;

        // Show modal
        document.getElementById('noteEditorModal').classList.add('show');
        document.body.style.overflow = 'hidden';

        // Focus title input and setup editor
        setTimeout(() => {
            document.getElementById('noteTitle').focus();
            // Setup image controls for existing images
            this.setupAllImageControls();
            // Apply current theme
            this.applyThemeToEditor();
        }, 300);

        this.updateWordCount();
        this.updateLastModified();
    }

    closeEditor() {
        if (this.currentNoteId) {
            this.saveCurrentNote();
        }
        
        document.getElementById('noteEditorModal').classList.remove('show');
        document.body.style.overflow = 'auto';
        this.currentNoteId = null;
    }

    debouncedSave() {
        // Clear existing timeout
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }

        // Set new timeout for 1 second
        this.saveTimeout = setTimeout(() => {
            this.saveCurrentNote();
        }, 1000);
    }

    debouncedRender() {
        // Clear existing timeout
        if (this.renderTimeout) {
            clearTimeout(this.renderTimeout);
        }

        // Set new timeout for 2 seconds to avoid frequent re-rendering
        this.renderTimeout = setTimeout(() => {
            this.renderNotes();
        }, 2000);
    }

    // Manual render for important actions only
    forceRender() {
        this.renderNotes();
    }

    saveCurrentNote() {
        if (!this.currentNoteId) return;

        const note = this.notes.get(this.currentNoteId);
        if (!note) return;

        note.title = document.getElementById('noteTitle').value || 'নতুন নোট';
        note.content = this.joditEditor.value;
        note.updatedAt = new Date().toISOString();

        this.notes.set(this.currentNoteId, note);
        this.saveNotesToStorage();
        // Don't render during auto-save to prevent shaking
        this.showSaveStatus();
        this.updateLastModified();
    }

    deleteCurrentNote() {
        if (!this.currentNoteId) return;

        if (confirm('আপনি কি এই নোটটি মুছে ফেলতে চান?')) {
            this.notes.delete(this.currentNoteId);
            this.saveNotesToStorage();
            this.forceRender(); // Force render for delete
            this.closeEditor();
        }
    }

    deleteNote(noteId) {
        if (confirm('আপনি কি এই নোটটি মুছে ফেলতে চান?')) {
            this.notes.delete(noteId);
            this.saveNotesToStorage();
            this.forceRender(); // Force render for delete
        }
    }

    renderNotes() {
        const grid = document.getElementById('notesGrid');
        grid.innerHTML = '';

        if (this.notes.size === 0) {
            grid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #7f8c8d;">
                    <i class="fas fa-sticky-note" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.3;"></i>
                    <h3>কোন নোট নেই</h3>
                    <p>নতুন নোট তৈরি করতে উপরের "নতুন নোট" বাটনে ক্লিক করুন</p>
                </div>
            `;
            return;
        }

        // Sort notes based on selected option
        const sortOption = document.getElementById('noteSortSelect').value;
        const sortedNotes = this.sortNotes(Array.from(this.notes.values()), sortOption);

        sortedNotes.forEach(note => {
            const noteCard = this.createNoteCard(note);
            grid.appendChild(noteCard);
        });
    }

    createNoteCard(note) {
        const div = document.createElement('div');
        div.className = 'note-card new';
        div.onclick = () => this.openEditor(note.id);

        // Create preview text (strip HTML tags)
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = note.content;
        const previewText = tempDiv.textContent || tempDiv.innerText || '';

        // Format dates
        const createdDate = new Date(note.createdAt);
        const updatedDate = new Date(note.updatedAt);

        const formatFullDate = (date) => {
            return date.toLocaleDateString('bn-BD', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        const priorityText = {
            low: '🟢 নিম্ন',
            medium: '🟡 মাঝারি',
            high: '🟠 উচ্চ',
            urgent: '🔴 জরুরি'
        };

        div.innerHTML = `
            <div class="note-header">
                <div class="note-title-row">
                    <h3 class="note-title">${this.escapeHtml(note.title)}</h3>
                    <span class="note-priority ${note.priority || 'medium'}">${priorityText[note.priority || 'medium']}</span>
                </div>
                <div class="note-dates">
                    <div class="note-date">
                        <i class="fas fa-plus"></i> তৈরি: ${formatFullDate(createdDate)}
                    </div>
                    <div class="note-date">
                        <i class="fas fa-edit"></i> আপডেট: ${formatFullDate(updatedDate)}
                    </div>
                </div>
            </div>
            <div class="note-preview">${previewText.substring(0, 100)}${previewText.length > 100 ? '...' : ''}</div>
            <div class="note-actions">
                <button class="note-action-btn" onclick="event.stopPropagation(); newNotesManager.openEditor('${note.id}')">
                    <i class="fas fa-edit"></i> সম্পাদনা
                </button>
                <button class="note-action-btn" onclick="event.stopPropagation(); newNotesManager.printNote('${note.id}')">
                    <i class="fas fa-print"></i> প্রিন্ট
                </button>
                <button class="note-action-btn delete" onclick="event.stopPropagation(); newNotesManager.deleteNote('${note.id}')">
                    <i class="fas fa-trash"></i> মুছুন
                </button>
            </div>
        `;

        return div;
    }

    insertImage() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = `<img src="${e.target.result}" style="max-width: 100%; height: auto; border-radius: 8px; margin: 10px 0;" />`;
                    this.joditEditor.selection.insertHTML(img);
                };
                reader.readAsDataURL(file);
            }
        };
        input.click();
    }

    insertDateTime() {
        const now = new Date();
        const dateTime = now.toLocaleDateString('bn-BD', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        this.joditEditor.selection.insertHTML(`<p><strong>📅 ${dateTime}</strong></p>`);
    }

    toggleFullscreen() {
        const modal = document.getElementById('noteEditorModal');
        const fullscreenBtn = document.getElementById('toggleFullscreen');
        const icon = fullscreenBtn.querySelector('i');

        if (modal.classList.contains('fullscreen')) {
            // Exit fullscreen
            modal.classList.remove('fullscreen');
            icon.className = 'fas fa-expand';
            fullscreenBtn.title = 'ফুল স্ক্রিন';
        } else {
            // Enter fullscreen
            modal.classList.add('fullscreen');
            icon.className = 'fas fa-compress';
            fullscreenBtn.title = 'ফুল স্ক্রিন থেকে বের হন';
        }

        // Small delay to ensure CSS transition completes
        setTimeout(() => {
            console.log('Fullscreen toggled');
        }, 100);
    }

    updateNoteTitle() {
        if (this.currentNoteId) {
            const note = this.notes.get(this.currentNoteId);
            if (note) {
                note.title = document.getElementById('noteTitle').value || 'নতুন নোট';
                note.updatedAt = new Date().toISOString();
                this.notes.set(this.currentNoteId, note);
                this.saveNotesToStorage();
                // Don't render during typing to prevent shaking
                this.showSaveStatus();
                this.updateLastModified();
            }
        }
    }

    updateWordCount() {
        if (!this.joditEditor) return;
        
        const content = this.joditEditor.getEditorValue();
        const words = content.trim() ? content.trim().split(/\s+/).length : 0;
        const chars = content.length;
        
        document.getElementById('wordCount').textContent = `${this.toBanglaNumber(words)} শব্দ`;
        document.getElementById('charCount').textContent = `${this.toBanglaNumber(chars)} অক্ষর`;
    }

    updateLastModified() {
        const now = new Date().toLocaleString('bn-BD');
        document.getElementById('lastModified').textContent = `সর্বশেষ পরিবর্তন: ${now}`;
    }

    markAsModified() {
        const statusEl = document.getElementById('autoSaveStatus');
        statusEl.innerHTML = '<i class="fas fa-clock"></i> পরিবর্তন হয়েছে';
        statusEl.style.color = '#ffc107';
    }

    showSaveStatus() {
        const statusEl = document.getElementById('autoSaveStatus');
        statusEl.innerHTML = '<i class="fas fa-check-circle"></i> তাৎক্ষণিক সেভ';
        statusEl.style.color = '#28a745';

        // Brief flash effect to show save
        statusEl.style.transform = 'scale(1.05)';
        setTimeout(() => {
            statusEl.style.transform = 'scale(1)';
        }, 200);
    }

    startAutoSave() {
        // No longer needed as we have instant auto-save
        // Keep the method for compatibility
        console.log('Instant auto-save enabled');
    }

    saveNotesToStorage() {
        const notesArray = Array.from(this.notes.entries());
        localStorage.setItem('newNotesData', JSON.stringify(notesArray));
    }

    loadNotesFromStorage() {
        const saved = localStorage.getItem('newNotesData');
        if (saved) {
            try {
                const notesArray = JSON.parse(saved);
                this.notes = new Map(notesArray);
            } catch (e) {
                console.error('Error loading notes:', e);
                this.notes = new Map();
            }
        }
    }

    toBanglaNumber(num) {
        const banglaDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
        return num.toString().replace(/\d/g, digit => banglaDigits[digit]);
    }

    setupImageControls(image) {
        if (!image || image.hasAttribute('data-controls-added')) return;

        image.setAttribute('data-controls-added', 'true');
        image.style.position = 'relative';
        image.style.cursor = 'pointer';
        image.style.border = '2px solid transparent';
        image.style.borderRadius = '8px';
        image.style.transition = 'all 0.3s ease';

        // Add hover effect
        image.addEventListener('mouseenter', () => {
            image.style.border = '2px solid #667eea';
            this.showImageControls(image);
        });

        image.addEventListener('mouseleave', () => {
            image.style.border = '2px solid transparent';
            this.hideImageControls(image);
        });

        // Add click event for resizing
        image.addEventListener('click', (e) => {
            e.preventDefault();
            this.showImageResizeDialog(image);
        });
    }

    setupAllImageControls() {
        const images = this.joditEditor.editor.querySelectorAll('img');
        images.forEach(img => this.setupImageControls(img));
    }

    showImageControls(image) {
        // Remove existing controls
        this.hideImageControls(image);

        const controls = document.createElement('div');
        controls.className = 'image-controls';
        controls.style.cssText = `
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
            z-index: 1000;
        `;

        const resizeBtn = document.createElement('button');
        resizeBtn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i>';
        resizeBtn.style.cssText = `
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 8px;
            cursor: pointer;
            font-size: 12px;
        `;
        resizeBtn.onclick = (e) => {
            e.stopPropagation();
            this.showImageResizeDialog(image);
        };

        const deleteBtn = document.createElement('button');
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.style.cssText = `
            background: rgba(220,53,69,0.9);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 8px;
            cursor: pointer;
            font-size: 12px;
        `;
        deleteBtn.onclick = (e) => {
            e.stopPropagation();
            if (confirm('এই ছবিটি মুছে ফেলতে চান?')) {
                image.remove();
                this.markAsModified();
            }
        };

        controls.appendChild(resizeBtn);
        controls.appendChild(deleteBtn);

        // Position controls relative to image
        const imageRect = image.getBoundingClientRect();
        const editorRect = this.joditEditor.editor.getBoundingClientRect();

        controls.style.position = 'absolute';
        controls.style.top = (imageRect.top - editorRect.top + 5) + 'px';
        controls.style.right = '5px';

        this.joditEditor.editor.appendChild(controls);
        image.setAttribute('data-controls-id', Date.now());
    }

    hideImageControls(image) {
        const existingControls = this.joditEditor.editor.querySelectorAll('.image-controls');
        existingControls.forEach(control => control.remove());
    }

    showImageResizeDialog(image) {
        const currentWidth = image.style.width || image.width || 'auto';
        const currentHeight = image.style.height || image.height || 'auto';

        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10001;
            min-width: 300px;
        `;

        dialog.innerHTML = `
            <h3 style="margin-top: 0;">ছবির সাইজ পরিবর্তন করুন</h3>
            <div style="margin: 15px 0;">
                <label>প্রস্থ:</label>
                <input type="text" id="imageWidth" value="${currentWidth}" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin: 15px 0;">
                <label>উচ্চতা:</label>
                <input type="text" id="imageHeight" value="${currentHeight}" style="width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin: 15px 0;">
                <button onclick="this.parentElement.parentElement.querySelector('#imageWidth').value='100%'; this.parentElement.parentElement.querySelector('#imageHeight').value='auto';" style="padding: 5px 10px; margin-right: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">পূর্ণ প্রস্থ</button>
                <button onclick="this.parentElement.parentElement.querySelector('#imageWidth').value='50%'; this.parentElement.parentElement.querySelector('#imageHeight').value='auto';" style="padding: 5px 10px; margin-right: 5px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">অর্ধেক</button>
                <button onclick="this.parentElement.parentElement.querySelector('#imageWidth').value='25%'; this.parentElement.parentElement.querySelector('#imageHeight').value='auto';" style="padding: 5px 10px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">এক চতুর্থাংশ</button>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button id="cancelResize" style="padding: 8px 15px; margin-right: 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">বাতিল</button>
                <button id="applyResize" style="padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">প্রয়োগ করুন</button>
            </div>
        `;

        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
        `;

        document.body.appendChild(overlay);
        document.body.appendChild(dialog);

        dialog.querySelector('#applyResize').onclick = () => {
            const width = dialog.querySelector('#imageWidth').value;
            const height = dialog.querySelector('#imageHeight').value;

            image.style.width = width;
            image.style.height = height;
            image.style.maxWidth = '100%';

            this.markAsModified();
            document.body.removeChild(overlay);
            document.body.removeChild(dialog);
        };

        dialog.querySelector('#cancelResize').onclick = () => {
            document.body.removeChild(overlay);
            document.body.removeChild(dialog);
        };

        overlay.onclick = () => {
            document.body.removeChild(overlay);
            document.body.removeChild(dialog);
        };
    }

    sortNotes(notes, sortOption) {
        return notes.sort((a, b) => {
            switch (sortOption) {
                case 'updated_desc':
                    return new Date(b.updatedAt) - new Date(a.updatedAt);
                case 'updated_asc':
                    return new Date(a.updatedAt) - new Date(b.updatedAt);
                case 'created_desc':
                    return new Date(b.createdAt) - new Date(a.createdAt);
                case 'created_asc':
                    return new Date(a.createdAt) - new Date(b.createdAt);
                case 'title_asc':
                    return a.title.localeCompare(b.title, 'bn');
                case 'title_desc':
                    return b.title.localeCompare(a.title, 'bn');
                case 'priority_desc':
                    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
                    return (priorityOrder[b.priority] || 2) - (priorityOrder[a.priority] || 2);
                case 'priority_asc':
                    const priorityOrderAsc = { urgent: 4, high: 3, medium: 2, low: 1 };
                    return (priorityOrderAsc[a.priority] || 2) - (priorityOrderAsc[b.priority] || 2);
                default:
                    return new Date(b.updatedAt) - new Date(a.updatedAt);
            }
        });
    }

    updateNotePriority() {
        if (this.currentNoteId) {
            const note = this.notes.get(this.currentNoteId);
            if (note) {
                note.priority = document.getElementById('notePriority').value;
                note.updatedAt = new Date().toISOString();
                this.notes.set(this.currentNoteId, note);
                this.saveNotesToStorage();
                // Don't render during typing to prevent shaking
                this.showSaveStatus();
                this.updateLastModified();
            }
        }
    }

    printNote(noteId) {
        const note = this.notes.get(noteId);
        if (!note) return;

        const printWindow = window.open('', '_blank');
        const priorityText = {
            low: '🟢 নিম্ন অগ্রাধিকার',
            medium: '🟡 মাঝারি অগ্রাধিকার',
            high: '🟠 উচ্চ অগ্রাধিকার',
            urgent: '🔴 জরুরি অগ্রাধিকার'
        };

        const createdDate = new Date(note.createdAt).toLocaleDateString('bn-BD', {
            year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
        });
        const updatedDate = new Date(note.updatedAt).toLocaleDateString('bn-BD', {
            year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
        });

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${note.title} - নোট প্রিন্ট</title>
                <meta charset="UTF-8">
                <style>
                    body { font-family: 'Noto Sans Bengali', Arial, sans-serif; margin: 40px; line-height: 1.6; }
                    .header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
                    .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                    .priority { display: inline-block; padding: 5px 10px; border-radius: 15px; font-size: 14px; margin-bottom: 10px; }
                    .priority.low { background: #d4edda; color: #155724; }
                    .priority.medium { background: #fff3cd; color: #856404; }
                    .priority.high { background: #f8d7da; color: #721c24; }
                    .priority.urgent { background: #f5c6cb; color: #721c24; font-weight: bold; }
                    .dates { font-size: 12px; color: #666; margin-bottom: 20px; }
                    .content { margin-top: 20px; }
                    .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
                    @media print { body { margin: 20px; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="title">${note.title}</div>
                    <div class="priority ${note.priority || 'medium'}">${priorityText[note.priority || 'medium']}</div>
                    <div class="dates">
                        <div>📅 তৈরি করা হয়েছে: ${createdDate}</div>
                        <div>✏️ সর্বশেষ আপডেট: ${updatedDate}</div>
                    </div>
                </div>
                <div class="content">
                    ${note.content}
                </div>
                <div class="footer">
                    <div>প্রিন্ট করা হয়েছে: ${new Date().toLocaleDateString('bn-BD', { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' })}</div>
                    <div>My Wallet - নোট ম্যানেজার</div>
                </div>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    }

    printCurrentNote() {
        if (this.currentNoteId) {
            this.printNote(this.currentNoteId);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is loaded
let newNotesManager;
document.addEventListener('DOMContentLoaded', () => {
    if (typeof Jodit !== 'undefined') {
        newNotesManager = new NewNotesManager();
    } else {
        console.error('Jodit Editor not loaded');
    }
});
