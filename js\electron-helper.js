// Electron Helper Functions
// This file provides helper functions for Electron integration

class ElectronHelper {
    constructor() {
        this.isElectron = this.checkIfElectron();
        this.electronAPI = this.isElectron ? window.electronAPI : null;

        if (this.isElectron) {
            console.log('🚀 Running in Electron environment with secure API');
            this.initElectronFeatures();
        } else {
            console.log('🌐 Running in browser environment');
        }
    }

    // Check if running in Electron (updated for secure context)
    checkIfElectron() {
        return typeof window !== 'undefined' &&
               typeof window.electronAPI === 'object' &&
               window.electronAPI !== null;
    }

    // Initialize Electron-specific features
    initElectronFeatures() {
        // Wait for DOM to be ready before accessing document.body
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupElectronFeatures();
            });
        } else {
            this.setupElectronFeatures();
        }
    }

    // Setup Electron features after DOM is ready
    setupElectronFeatures() {
        // Add Electron-specific CSS class
        if (document.body) {
            document.body.classList.add('electron-app');
        }

        // Handle window controls if needed
        this.setupWindowControls();

        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Setup file operations
        this.setupFileOperations();

        // Fix prompt function
        this.fixPromptFunction();
    }

    // Setup window controls
    setupWindowControls() {
        // You can add custom window controls here if needed
        console.log('Window controls initialized');
    }

    // Setup keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+R or F5 - Reload
            if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                e.preventDefault();
                location.reload();
            }
            
            // F11 - Toggle fullscreen
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
            
            // F12 - Toggle DevTools (only in development)
            if (e.key === 'F12') {
                e.preventDefault();
                this.toggleDevTools();
            }
            
            // Ctrl+Shift+I - Toggle DevTools
            if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                e.preventDefault();
                this.toggleDevTools();
            }
        });
    }

    // Setup file operations
    setupFileOperations() {
        // Add file operation methods here
        console.log('File operations initialized');
    }

    // Toggle fullscreen
    toggleFullscreen() {
        if (this.isElectron) {
            const { remote } = require('electron');
            const win = remote.getCurrentWindow();
            win.setFullScreen(!win.isFullScreen());
        } else {
            // Fallback for browser
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
    }

    // Toggle DevTools
    toggleDevTools() {
        if (this.isElectron) {
            const { remote } = require('electron');
            const win = remote.getCurrentWindow();
            win.webContents.toggleDevTools();
        }
    }

    // Show save dialog
    async showSaveDialog() {
        if (this.isElectron && this.ipcRenderer) {
            return await this.ipcRenderer.invoke('show-save-dialog');
        }
        return null;
    }

    // Show open dialog
    async showOpenDialog() {
        if (this.isElectron && this.ipcRenderer) {
            return await this.ipcRenderer.invoke('show-open-dialog');
        }
        return null;
    }

    // Write file
    async writeFile(filePath, data) {
        if (this.isElectron && this.ipcRenderer) {
            return await this.ipcRenderer.invoke('write-file', filePath, data);
        }
        return { success: false, error: 'Not running in Electron' };
    }

    // Read file
    async readFile(filePath) {
        if (this.isElectron && this.ipcRenderer) {
            return await this.ipcRenderer.invoke('read-file', filePath);
        }
        return { success: false, error: 'Not running in Electron' };
    }

    // Get app version
    async getAppVersion() {
        if (this.isElectron && this.ipcRenderer) {
            return await this.ipcRenderer.invoke('app-version');
        }
        return '1.0.0';
    }

    // Show notification (Electron native)
    showNotification(title, body, options = {}) {
        if (this.isElectron) {
            const { remote } = require('electron');
            new remote.Notification({
                title: title,
                body: body,
                ...options
            }).show();
        } else {
            // Fallback to browser notification
            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification(title, { body, ...options });
                } else if (Notification.permission !== 'denied') {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            new Notification(title, { body, ...options });
                        }
                    });
                }
            }
        }
    }

    // Export data with save dialog
    async exportData(data, defaultFileName = 'my-finance-data.json') {
        try {
            const result = await this.showSaveDialog();
            if (result && !result.canceled) {
                const filePath = result.filePath || defaultFileName;
                const jsonData = JSON.stringify(data, null, 2);
                const writeResult = await this.writeFile(filePath, jsonData);
                
                if (writeResult.success) {
                    this.showNotification('সফল!', 'ডেটা সফলভাবে এক্সপোর্ট হয়েছে');
                    return true;
                } else {
                    this.showNotification('ত্রুটি!', 'ডেটা এক্সপোর্ট করতে সমস্যা হয়েছে');
                    return false;
                }
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification('ত্রুটি!', 'ডেটা এক্সপোর্ট করতে সমস্যা হয়েছে');
            return false;
        }
    }

    // Import data with open dialog
    async importData() {
        try {
            const result = await this.showOpenDialog();
            if (result && !result.canceled && result.filePaths.length > 0) {
                const filePath = result.filePaths[0];
                const readResult = await this.readFile(filePath);

                if (readResult.success) {
                    const data = JSON.parse(readResult.data);
                    this.showNotification('সফল!', 'ডেটা সফলভাবে ইমপোর্ট হয়েছে');
                    return data;
                } else {
                    this.showNotification('ত্রুটি!', 'ফাইল পড়তে সমস্যা হয়েছে');
                    return null;
                }
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification('ত্রুটি!', 'ডেটা ইমপোর্ট করতে সমস্যা হয়েছে');
            return null;
        }
    }

    // Fix prompt function for Electron
    fixPromptFunction() {
        if (this.isElectron) {
            // Override prompt function to work in Electron
            window.originalPrompt = window.prompt;
            window.prompt = (message, defaultValue = '') => {
                // Use a simple input dialog
                const result = window.originalPrompt ? window.originalPrompt(message, defaultValue) : null;
                return result;
            };
        }
    }
}

// Initialize Electron Helper
const electronHelper = new ElectronHelper();

// Make it globally available
window.electronHelper = electronHelper;
