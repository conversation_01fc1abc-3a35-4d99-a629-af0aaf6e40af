/**
 * Local Bengali Fonts for Electron App
 * No external dependencies, no MIME type errors
 */

/* Noto Sans Bengali - Primary choice */
@font-face {
    font-family: 'Noto Sans Bengali Local';
    src: local('Noto Sans Bengali'),
         local('NotoSansBengali-Regular'),
         local('Noto Sans Bengali Regular');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Bengali Local';
    src: local('Noto Sans Bengali Medium'),
         local('NotoSansBengali-Medium'),
         local('Noto Sans Bengali Medium');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Bengali Local';
    src: local('Noto Sans Bengali SemiBold'),
         local('NotoSansBengali-SemiBold'),
         local('Noto Sans Bengali SemiBold');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans Bengali Local';
    src: local('Noto Sans Bengali Bold'),
         local('NotoSansBengali-Bold'),
         local('Noto Sans Bengali Bold');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Kalpurush - Secondary choice */
@font-face {
    font-family: 'Kalpurush Local';
    src: local('Kalpurush'),
         local('Kalpurush Regular');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Kalpurush Local';
    src: local('Kalpurush Medium'),
         local('Kalpurush-Medium');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Kalpurush Local';
    src: local('Kalpurush SemiBold'),
         local('Kalpurush-SemiBold');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Kalpurush Local';
    src: local('Kalpurush Bold'),
         local('Kalpurush-Bold');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* SolaimanLipi - Windows built-in */
@font-face {
    font-family: 'SolaimanLipi Local';
    src: local('SolaimanLipi'),
         local('Solaiman Lipi');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Nikosh - Linux built-in */
@font-face {
    font-family: 'Nikosh Local';
    src: local('Nikosh'),
         local('Nikosh Regular');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Vrinda - Windows built-in */
@font-face {
    font-family: 'Vrinda Local';
    src: local('Vrinda'),
         local('Vrinda Regular');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Font stack definitions */
:root {
    /* Primary Bengali font stack - most reliable */
    --bengali-font-primary: 'Noto Sans Bengali Local', 'Noto Sans Bengali', 'Kalpurush Local', 'Kalpurush';
    
    /* System Bengali fonts */
    --bengali-font-system: 'SolaimanLipi Local', 'SolaimanLipi', 'Nikosh Local', 'Nikosh', 'Vrinda Local', 'Vrinda';
    
    /* Complete Bengali font stack */
    --bengali-font-complete: var(--bengali-font-primary), var(--bengali-font-system), 'Shonar Bangla', 'Bangla', sans-serif;
    
    /* Number fonts (for better number display) */
    --number-font: var(--bengali-font-complete);
    
    /* English fonts */
    --english-font: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
}

/* Apply fonts to elements */
body {
    font-family: var(--bengali-font-complete);
    font-weight: 400;
    line-height: 1.6;
}

/* Bengali text specific */
.bengali-text,
[lang="bn"],
[lang="bn-BD"] {
    font-family: var(--bengali-font-complete) !important;
}

/* Numbers and amounts */
.amount,
.balance,
.total,
.currency,
.price,
.value,
.count,
.number,
[class*="amount"],
[class*="balance"],
[class*="total"] {
    font-family: var(--number-font) !important;
    font-variant-numeric: tabular-nums;
}

/* English text specific */
.english-text,
[lang="en"] {
    font-family: var(--english-font) !important;
}

/* Font loading states */
.font-loading {
    font-family: var(--bengali-font-system), sans-serif !important;
    visibility: visible !important;
}

.font-loaded {
    font-family: var(--bengali-font-complete) !important;
}

.font-fallback {
    font-family: var(--bengali-font-system), sans-serif !important;
}

/* Ensure text is always readable */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Font weight classes */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Responsive font sizes */
@media screen and (max-width: 768px) {
    body {
        font-size: 14px;
        line-height: 1.5;
    }
}

@media screen and (min-width: 1200px) {
    body {
        font-size: 16px;
        line-height: 1.6;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}
