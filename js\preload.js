/**
 * Preload Script for Secure Electron IPC Communication
 * This script runs in the renderer process but has access to Node.js APIs
 * It provides a secure bridge between the main and renderer processes
 */

const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getAppVersion: () => ipcRenderer.invoke('app-version'),
  
  // File operations
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  
  // Notification system
  showNotification: (title, body, options = {}) => {
    return ipcRenderer.invoke('show-notification', { title, body, ...options });
  },
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // Theme and settings
  getTheme: () => ipcRenderer.invoke('get-theme'),
  setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
  
  // Data backup and restore
  exportData: (data) => ipcRenderer.invoke('export-data', data),
  importData: () => ipcRenderer.invoke('import-data'),
  
  // Print functionality
  printPage: () => ipcRenderer.invoke('print-page'),
  
  // Security: Only expose safe methods
  platform: process.platform,
  
  // Event listeners for main process communications
  onThemeChanged: (callback) => {
    ipcRenderer.on('theme-changed', callback);
    // Return a cleanup function
    return () => ipcRenderer.removeListener('theme-changed', callback);
  },
  
  onAppUpdate: (callback) => {
    ipcRenderer.on('app-update', callback);
    return () => ipcRenderer.removeListener('app-update', callback);
  }
});

// Expose a limited console for debugging (only in development)
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('electronConsole', {
    log: (...args) => console.log('[Renderer]', ...args),
    warn: (...args) => console.warn('[Renderer]', ...args),
    error: (...args) => console.error('[Renderer]', ...args)
  });
}

// Security: Remove any global Node.js objects that might have been exposed
delete global.require;
delete global.exports;
delete global.module;

// Add security headers and CSP
window.addEventListener('DOMContentLoaded', () => {
  // Add Content Security Policy meta tag if not present
  if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
    const cspMeta = document.createElement('meta');
    cspMeta.httpEquiv = 'Content-Security-Policy';
    cspMeta.content = `
      default-src 'self';
      script-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://cdnjs.cloudflare.com;
      font-src 'self' https://fonts.gstatic.com https://fonts.bunny.net https://cdnjs.cloudflare.com;
      img-src 'self' data: https:;
      connect-src 'self' https:;
      media-src 'self';
      object-src 'none';
      base-uri 'self';
      form-action 'self';
      frame-ancestors 'none';
    `.replace(/\s+/g, ' ').trim();
    document.head.appendChild(cspMeta);
  }
});

// Prevent context menu in production
if (process.env.NODE_ENV === 'production') {
  window.addEventListener('contextmenu', (e) => {
    e.preventDefault();
  });
}

// Prevent drag and drop of files
window.addEventListener('dragover', (e) => {
  e.preventDefault();
});

window.addEventListener('drop', (e) => {
  e.preventDefault();
});

// Security: Prevent navigation to external URLs
window.addEventListener('beforeunload', (e) => {
  // Allow normal page unload
  return undefined;
});

// Log that preload script has loaded
console.log('🔒 Preload script loaded with security features enabled');
